<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>考试</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 考试页面样式 */
        .exam-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .exam-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .exam-tab {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: var(--bg-tertiary);
        }
        
        .exam-tab.active {
            background: var(--primary-color);
            color: white;
        }
        
        .exam-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .summary-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
        }
        
        .summary-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .summary-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: var(--margin-xs);
        }
        
        .summary-number.upcoming {
            color: var(--warning-color);
        }
        
        .summary-number.completed {
            color: var(--success-color);
        }
        
        .summary-number.missed {
            color: var(--error-color);
        }
        
        .summary-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .exam-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .exam-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .exam-item:last-child {
            border-bottom: none;
        }
        
        .exam-item:active {
            background: var(--bg-color-active);
        }
        
        .exam-item.upcoming {
            border-left: 4px solid var(--warning-color);
        }
        
        .exam-item.completed {
            border-left: 4px solid var(--success-color);
        }
        
        .exam-item.missed {
            border-left: 4px solid var(--error-color);
        }
        
        .exam-item.urgent {
            background: var(--warning-light);
        }
        
        .exam-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .exam-course {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .exam-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-upcoming {
            background: var(--warning-color);
            color: white;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .status-missed {
            background: var(--error-color);
            color: white;
        }
        
        .exam-time {
            background: var(--primary-light);
            color: var(--primary-color);
            border-radius: 6px;
            padding: var(--padding-xs) var(--padding-sm);
            font-size: var(--font-size-small);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            display: inline-block;
        }
        
        .exam-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .exam-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .exam-countdown {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            text-align: center;
            margin-bottom: var(--margin-sm);
        }
        
        .countdown-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
        }
        
        .countdown-time {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--warning-color);
        }
        
        .exam-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-remind {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-score {
            background: var(--success-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .exam-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .exam-detail-modal.show {
            display: flex;
        }
        
        .exam-detail-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
        }
        
        .exam-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .exam-detail-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .exam-detail-close {
            color: var(--text-secondary);
            cursor: pointer;
            font-size: var(--font-size-h4);
        }
        
        .exam-detail-body {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .detail-section {
            margin-bottom: var(--margin-md);
        }
        
        .detail-section:last-child {
            margin-bottom: 0;
        }
        
        .detail-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--margin-xs);
        }
        
        .detail-label {
            color: var(--text-secondary);
        }
        
        .detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">考试</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="exam-header">
            <div class="header-title">考试</div>
            <div class="header-subtitle">查看考试安排和成绩</div>
        </div>

        <!-- 考试标签 -->
        <div class="exam-tabs">
            <div class="exam-tab active" data-tab="upcoming" onclick="switchTab('upcoming')">即将考试</div>
            <div class="exam-tab" data-tab="completed" onclick="switchTab('completed')">已完成</div>
            <div class="exam-tab" data-tab="missed" onclick="switchTab('missed')">已错过</div>
        </div>

        <!-- 考试汇总 -->
        <div class="exam-summary">
            <div class="summary-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                <span>考试统计</span>
            </div>

            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-number upcoming" id="upcomingCount">0</div>
                    <div class="summary-label">即将考试</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number completed" id="completedCount">0</div>
                    <div class="summary-label">已完成</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number missed" id="missedCount">0</div>
                    <div class="summary-label">已错过</div>
                </div>
            </div>
        </div>

        <!-- 考试列表 -->
        <div class="exam-list">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-calendar"></i>
                    <span id="listTitle">即将考试</span>
                </div>
                <div class="list-count" id="examCount">0</div>
            </div>

            <div id="examItems">
                <!-- 考试列表将动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-calendar"></i>
            <div id="emptyMessage">暂无考试安排</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 考试详情模态框 -->
    <div class="exam-detail-modal" id="examDetailModal">
        <div class="exam-detail-content">
            <div class="exam-detail-header">
                <div class="exam-detail-title" id="examDetailTitle">考试详情</div>
                <div class="exam-detail-close" onclick="closeExamDetail();">
                    <i class="ace-icon fa fa-times"></i>
                </div>
            </div>
            <div class="exam-detail-body" id="examDetailBody">
                <!-- 考试详情内容将动态填充 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allExams = [];
        let currentTab = 'upcoming';
        let summaryData = {};
        let countdownIntervals = {};

        $(function() {
            initPage();
            loadSummaryData();
            loadExamData();
            startCountdownUpdates();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载汇总数据
        function loadSummaryData() {
            $.ajax({
                url: "/student/exam/getSummaryData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    summaryData = data.summary || {};
                    renderSummaryData();
                },
                error: function() {
                    console.log('加载汇总数据失败');
                }
            });
        }

        // 渲染汇总数据
        function renderSummaryData() {
            $('#upcomingCount').text(summaryData.upcomingCount || 0);
            $('#completedCount').text(summaryData.completedCount || 0);
            $('#missedCount').text(summaryData.missedCount || 0);
        }

        // 加载考试数据
        function loadExamData() {
            showLoading(true);

            $.ajax({
                url: "/student/exam/getExamData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allExams = data.exams || [];
                    renderExamList();
                    showLoading(false);
                },
                error: function() {
                    showError('加载考试数据失败');
                    showLoading(false);
                }
            });
        }

        // 切换标签
        function switchTab(tab) {
            currentTab = tab;

            // 更新标签状态
            $('.exam-tab').removeClass('active');
            $(`.exam-tab[data-tab="${tab}"]`).addClass('active');

            // 更新列表标题
            const titles = {
                'upcoming': '即将考试',
                'completed': '已完成考试',
                'missed': '已错过考试'
            };
            $('#listTitle').text(titles[tab]);

            renderExamList();
        }

        // 渲染考试列表
        function renderExamList() {
            const filteredExams = allExams.filter(exam => {
                const status = getExamStatus(exam);
                return status === currentTab;
            });

            $('#examCount').text(filteredExams.length);

            const container = $('#examItems');
            container.empty();

            if (filteredExams.length === 0) {
                const messages = {
                    'upcoming': '暂无即将进行的考试',
                    'completed': '暂无已完成的考试',
                    'missed': '暂无错过的考试'
                };
                showEmptyState(messages[currentTab]);
                return;
            } else {
                hideEmptyState();
            }

            // 按时间排序
            filteredExams.sort((a, b) => {
                const timeA = new Date(a.examTime);
                const timeB = new Date(b.examTime);
                return currentTab === 'upcoming' ? timeA - timeB : timeB - timeA;
            });

            filteredExams.forEach(exam => {
                const examHtml = createExamItem(exam);
                container.append(examHtml);
            });
        }

        // 创建考试项
        function createExamItem(exam) {
            const status = getExamStatus(exam);
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);
            const isUrgent = isExamUrgent(exam);

            let countdownHtml = '';
            if (status === 'upcoming') {
                const countdown = getCountdown(exam.examTime);
                countdownHtml = `
                    <div class="exam-countdown">
                        <div class="countdown-text">距离考试还有</div>
                        <div class="countdown-time" id="countdown-${exam.id}">${countdown}</div>
                    </div>
                `;
            }

            return `
                <div class="exam-item ${status} ${isUrgent ? 'urgent' : ''}" onclick="showExamDetail('${exam.id}')">
                    <div class="exam-basic">
                        <div class="exam-course">${exam.courseName}</div>
                        <div class="exam-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="exam-time">${formatExamTime(exam.examTime)}</div>
                    <div class="exam-details">
                        <div class="exam-detail-item">
                            <span>考试地点:</span>
                            <span>${exam.location || '待定'}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span>考试时长:</span>
                            <span>${exam.duration}分钟</span>
                        </div>
                        <div class="exam-detail-item">
                            <span>考试类型:</span>
                            <span>${getExamTypeText(exam.type)}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span>座位号:</span>
                            <span>${exam.seatNumber || '待安排'}</span>
                        </div>
                    </div>
                    ${countdownHtml}
                    <div class="exam-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showExamDetail('${exam.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${status === 'upcoming' ? `
                            <button class="btn-mobile btn-remind" onclick="event.stopPropagation(); setExamReminder('${exam.id}');">
                                <i class="ace-icon fa fa-bell"></i>
                                <span>提醒</span>
                            </button>
                        ` : status === 'completed' ? `
                            <button class="btn-mobile btn-score" onclick="event.stopPropagation(); viewExamScore('${exam.id}');">
                                <i class="ace-icon fa fa-star"></i>
                                <span>成绩</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-times"></i>
                                <span>已错过</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 获取考试状态
        function getExamStatus(exam) {
            const now = new Date();
            const examTime = new Date(exam.examTime);
            const examEndTime = new Date(examTime.getTime() + exam.duration * 60000);

            if (now < examTime) {
                return 'upcoming';
            } else if (now >= examTime && now <= examEndTime) {
                return 'upcoming'; // 正在进行的考试也算即将考试
            } else {
                return exam.hasScore ? 'completed' : 'missed';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return `status-${status}`;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'upcoming': return '即将考试';
                case 'completed': return '已完成';
                case 'missed': return '已错过';
                default: return '未知';
            }
        }

        // 判断考试是否紧急
        function isExamUrgent(exam) {
            const now = new Date();
            const examTime = new Date(exam.examTime);
            const timeDiff = examTime - now;
            return timeDiff > 0 && timeDiff <= 24 * 60 * 60 * 1000; // 24小时内
        }

        // 获取考试类型文本
        function getExamTypeText(type) {
            switch(type) {
                case 'final': return '期末考试';
                case 'midterm': return '期中考试';
                case 'quiz': return '随堂测验';
                case 'makeup': return '补考';
                default: return '其他';
            }
        }

        // 格式化考试时间
        function formatExamTime(timeStr) {
            const date = new Date(timeStr);
            const month = date.getMonth() + 1;
            const day = date.getDate();
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `${month}月${day}日 ${hours}:${minutes}`;
        }

        // 获取倒计时
        function getCountdown(examTimeStr) {
            const now = new Date();
            const examTime = new Date(examTimeStr);
            const timeDiff = examTime - now;

            if (timeDiff <= 0) {
                return '考试进行中';
            }

            const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

            if (days > 0) {
                return `${days}天${hours}小时`;
            } else if (hours > 0) {
                return `${hours}小时${minutes}分钟`;
            } else {
                return `${minutes}分钟`;
            }
        }

        // 开始倒计时更新
        function startCountdownUpdates() {
            setInterval(() => {
                $('.countdown-time').each(function() {
                    const examId = $(this).attr('id').replace('countdown-', '');
                    const exam = allExams.find(e => e.id === examId);
                    if (exam) {
                        const countdown = getCountdown(exam.examTime);
                        $(this).text(countdown);
                    }
                });
            }, 60000); // 每分钟更新一次
        }

        // 显示考试详情
        function showExamDetail(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            renderExamDetail(exam);
        }

        // 渲染考试详情
        function renderExamDetail(exam) {
            const status = getExamStatus(exam);
            const statusText = getStatusText(status);

            let detailHtml = `
                <div class="detail-section">
                    <div class="detail-section-title">基本信息</div>
                    <div class="detail-item">
                        <span class="detail-label">课程名称:</span>
                        <span class="detail-value">${exam.courseName}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">课程代码:</span>
                        <span class="detail-value">${exam.courseCode}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">考试状态:</span>
                        <span class="detail-value">${statusText}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">考试类型:</span>
                        <span class="detail-value">${getExamTypeText(exam.type)}</span>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="detail-section-title">时间地点</div>
                    <div class="detail-item">
                        <span class="detail-label">考试时间:</span>
                        <span class="detail-value">${formatFullTime(exam.examTime)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">考试时长:</span>
                        <span class="detail-value">${exam.duration}分钟</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">考试地点:</span>
                        <span class="detail-value">${exam.location || '待定'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">座位号:</span>
                        <span class="detail-value">${exam.seatNumber || '待安排'}</span>
                    </div>
                </div>
            `;

            if (exam.teacher) {
                detailHtml += `
                    <div class="detail-section">
                        <div class="detail-section-title">监考信息</div>
                        <div class="detail-item">
                            <span class="detail-label">监考教师:</span>
                            <span class="detail-value">${exam.teacher}</span>
                        </div>
                    </div>
                `;
            }

            if (exam.requirements) {
                detailHtml += `
                    <div class="detail-section">
                        <div class="detail-section-title">考试要求</div>
                        <div style="line-height: 1.6;">${exam.requirements}</div>
                    </div>
                `;
            }

            if (status === 'completed' && exam.score !== undefined) {
                detailHtml += `
                    <div class="detail-section">
                        <div class="detail-section-title">考试成绩</div>
                        <div class="detail-item">
                            <span class="detail-label">成绩:</span>
                            <span class="detail-value">${exam.score}分</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">等级:</span>
                            <span class="detail-value">${getGradeLevel(exam.score)}</span>
                        </div>
                    </div>
                `;
            }

            $('#examDetailTitle').text(`${exam.courseName} - 考试详情`);
            $('#examDetailBody').html(detailHtml);
            $('#examDetailModal').addClass('show');
        }

        // 关闭考试详情
        function closeExamDetail() {
            $('#examDetailModal').removeClass('show');
        }

        // 设置考试提醒
        function setExamReminder(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            const message = `确定要设置"${exam.courseName}"的考试提醒吗？\n\n考试时间：${formatFullTime(exam.examTime)}`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSetExamReminder(examId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSetExamReminder(examId);
                }
            }
        }

        // 执行设置考试提醒
        function doSetExamReminder(examId) {
            $.ajax({
                url: "/student/exam/setExamReminder",
                type: "post",
                data: { examId: examId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('考试提醒设置成功');
                    } else {
                        showError(data.message || '设置提醒失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 查看考试成绩
        function viewExamScore(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            if (exam.score !== undefined) {
                const message = `${exam.courseName} 考试成绩\n\n成绩：${exam.score}分\n等级：${getGradeLevel(exam.score)}\n考试时间：${formatFullTime(exam.examTime)}`;

                if (typeof urp !== 'undefined' && urp.alert) {
                    urp.alert(message);
                } else {
                    alert(message);
                }
            } else {
                showError('成绩尚未公布');
            }
        }

        // 获取成绩等级
        function getGradeLevel(score) {
            if (score >= 90) return '优秀';
            if (score >= 80) return '良好';
            if (score >= 70) return '中等';
            if (score >= 60) return '及格';
            return '不及格';
        }

        // 格式化完整时间
        function formatFullTime(timeStr) {
            const date = new Date(timeStr);
            return date.toLocaleString();
        }

        // 刷新数据
        function refreshData() {
            loadSummaryData();
            loadExamData();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#examDetailModal').click(function(e) {
            if (e.target === this) {
                closeExamDetail();
            }
        });
    </script>
</body>
</html>
